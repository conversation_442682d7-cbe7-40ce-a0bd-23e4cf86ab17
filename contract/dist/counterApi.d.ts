export interface Counter {
    value: number;
}
export declare class CounterContractAPI {
    private state;
    private isInitialized;
    initialize(): Promise<void>;
    increment(amount: number): Promise<void>;
    decrement(amount: number): Promise<void>;
    reset(): Promise<void>;
    getValue(): Promise<[number]>;
    private checkInitialized;
    private simulateBlockchainDelay;
    getStatus(): {
        initialized: boolean;
        currentValue: number;
    };
}
export declare const counterContract: CounterContractAPI;
//# sourceMappingURL=counterApi.d.ts.map