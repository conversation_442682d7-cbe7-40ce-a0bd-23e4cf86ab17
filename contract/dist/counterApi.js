"use strict";
// Simulated contract API that mimics the behavior of a compiled Compact contract
// In a real implementation, this would be generated by the Compact compiler
Object.defineProperty(exports, "__esModule", { value: true });
exports.counterContract = exports.CounterContractAPI = void 0;
class CounterContractAPI {
    constructor() {
        this.state = { value: 0 };
        this.isInitialized = false;
    }
    // Simulate contract initialization
    async initialize() {
        console.log('🚀 Initializing counter contract...');
        await this.simulateBlockchainDelay();
        this.state = { value: 0 };
        this.isInitialized = true;
        console.log('✅ Counter initialized with value 0');
    }
    // Simulate increment operation
    async increment(amount) {
        this.checkInitialized();
        console.log(`📈 Incrementing counter by ${amount}...`);
        await this.simulateBlockchainDelay();
        const oldValue = this.state.value;
        this.state.value += amount;
        console.log(`✅ Counter incremented from ${oldValue} to ${this.state.value}`);
    }
    // Simulate decrement operation with underflow protection
    async decrement(amount) {
        this.checkInitialized();
        console.log(`📉 Decrementing counter by ${amount}...`);
        await this.simulateBlockchainDelay();
        const oldValue = this.state.value;
        this.state.value = Math.max(0, this.state.value - amount);
        if (oldValue < amount) {
            console.log(`⚠️ Underflow protection: Counter set to 0 (was ${oldValue})`);
        }
        else {
            console.log(`✅ Counter decremented from ${oldValue} to ${this.state.value}`);
        }
    }
    // Simulate reset operation
    async reset() {
        this.checkInitialized();
        console.log('🔄 Resetting counter...');
        await this.simulateBlockchainDelay();
        const oldValue = this.state.value;
        this.state.value = 0;
        console.log(`✅ Counter reset from ${oldValue} to 0`);
    }
    // Get current counter value
    async getValue() {
        this.checkInitialized();
        console.log(`📊 Getting counter value: ${this.state.value}`);
        return [this.state.value];
    }
    // Check if contract is initialized
    checkInitialized() {
        if (!this.isInitialized) {
            throw new Error('Contract not initialized. Call initialize() first.');
        }
    }
    // Simulate blockchain transaction delay
    async simulateBlockchainDelay() {
        const delay = Math.random() * 1000 + 500; // 500-1500ms delay
        await new Promise(resolve => setTimeout(resolve, delay));
    }
    // Get contract status
    getStatus() {
        return {
            initialized: this.isInitialized,
            currentValue: this.state.value
        };
    }
}
exports.CounterContractAPI = CounterContractAPI;
// Export singleton instance
exports.counterContract = new CounterContractAPI();
//# sourceMappingURL=counterApi.js.map