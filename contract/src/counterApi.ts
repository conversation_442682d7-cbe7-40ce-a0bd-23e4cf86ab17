// Simulated contract API that mimics the behavior of a compiled Compact contract
// In a real implementation, this would be generated by the Compact compiler

export interface Counter {
  value: number;
}

export class CounterContractAPI {
  private state: Counter = { value: 0 };
  private isInitialized: boolean = false;

  // Simulate contract initialization
  async initialize(): Promise<void> {
    console.log('🚀 Initializing counter contract...');
    await this.simulateBlockchainDelay();
    this.state = { value: 0 };
    this.isInitialized = true;
    console.log('✅ Counter initialized with value 0');
  }

  // Simulate increment operation
  async increment(amount: number): Promise<void> {
    this.checkInitialized();
    console.log(`📈 Incrementing counter by ${amount}...`);
    await this.simulateBlockchainDelay();
    
    const oldValue = this.state.value;
    this.state.value += amount;
    
    console.log(`✅ Counter incremented from ${oldValue} to ${this.state.value}`);
  }

  // Simulate decrement operation with underflow protection
  async decrement(amount: number): Promise<void> {
    this.checkInitialized();
    console.log(`📉 Decrementing counter by ${amount}...`);
    await this.simulateBlockchainDelay();
    
    const oldValue = this.state.value;
    this.state.value = Math.max(0, this.state.value - amount);
    
    if (oldValue < amount) {
      console.log(`⚠️ Underflow protection: Counter set to 0 (was ${oldValue})`);
    } else {
      console.log(`✅ Counter decremented from ${oldValue} to ${this.state.value}`);
    }
  }

  // Simulate reset operation
  async reset(): Promise<void> {
    this.checkInitialized();
    console.log('🔄 Resetting counter...');
    await this.simulateBlockchainDelay();
    
    const oldValue = this.state.value;
    this.state.value = 0;
    
    console.log(`✅ Counter reset from ${oldValue} to 0`);
  }

  // Get current counter value
  async getValue(): Promise<[number]> {
    this.checkInitialized();
    console.log(`📊 Getting counter value: ${this.state.value}`);
    return [this.state.value];
  }

  // Check if contract is initialized
  private checkInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('Contract not initialized. Call initialize() first.');
    }
  }

  // Simulate blockchain transaction delay
  private async simulateBlockchainDelay(): Promise<void> {
    const delay = Math.random() * 1000 + 500; // 500-1500ms delay
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  // Get contract status
  getStatus(): { initialized: boolean; currentValue: number } {
    return {
      initialized: this.isInitialized,
      currentValue: this.state.value
    };
  }
}

// Export singleton instance
export const counterContract = new CounterContractAPI();
