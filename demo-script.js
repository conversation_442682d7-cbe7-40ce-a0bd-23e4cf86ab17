// Demo script to test the contract functionality
// Run this with: node demo-script.js

const { contractService } = require('./dapp-web/src/services/contractService.ts');

async function runDemo() {
  console.log('🌙 Midnight Counter DApp Demo');
  console.log('================================\n');

  try {
    // Initialize the contract
    console.log('1. Initializing contract...');
    await contractService.initialize();
    console.log('   ✅ Contract initialized\n');

    // Get initial value
    console.log('2. Getting initial value...');
    let value = await contractService.getValue();
    console.log(`   📊 Current value: ${value}\n`);

    // Increment by 5
    console.log('3. Incrementing by 5...');
    await contractService.increment(5);
    value = await contractService.getValue();
    console.log(`   📊 New value: ${value}\n`);

    // Increment by 3
    console.log('4. Incrementing by 3...');
    await contractService.increment(3);
    value = await contractService.getValue();
    console.log(`   📊 New value: ${value}\n`);

    // Decrement by 2
    console.log('5. Decrementing by 2...');
    await contractService.decrement(2);
    value = await contractService.getValue();
    console.log(`   📊 New value: ${value}\n`);

    // Try to decrement by more than current value (underflow protection)
    console.log('6. Testing underflow protection (decrement by 10)...');
    await contractService.decrement(10);
    value = await contractService.getValue();
    console.log(`   📊 New value: ${value}\n`);

    // Reset counter
    console.log('7. Resetting counter...');
    await contractService.reset();
    value = await contractService.getValue();
    console.log(`   📊 Final value: ${value}\n`);

    // Show transaction history
    console.log('8. Transaction History:');
    const history = contractService.getTransactionHistory();
    history.forEach((tx, index) => {
      console.log(`   ${index + 1}. ${tx.type.toUpperCase()}: ${tx.oldValue} → ${tx.newValue} ${tx.amount ? `(±${tx.amount})` : ''}`);
    });

    console.log('\n🎉 Demo completed successfully!');
    console.log('Now open http://localhost:3000 to interact with the web UI!');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  runDemo();
}

module.exports = { runDemo };
