{"version": 3, "file": "no-wrapper-object-types.js", "sourceRoot": "", "sources": ["../../src/rules/no-wrapper-object-types.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAkE;AAElE,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;IACzB,QAAQ;IACR,6EAA6E;IAC7E,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,6EAA6E;IAC7E,QAAQ;IACR,QAAQ;CACT,CAAC,CAAC;AAEH,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,yBAAyB;IAC/B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,4DAA4D;SAC1E;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,eAAe,EACb,wGAAwG;SAC3G;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,SAAS,gBAAgB,CACvB,IAA+C,EAC/C,UAAmB;YAEnB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC;YACtE,IACE,CAAC,QAAQ;gBACT,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACzB,CAAC,IAAA,kCAA2B,EAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,EAChE,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YAEzC,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE;gBAC7B,GAAG,EAAE,UAAU;oBACb,CAAC,CAAC,CAAC,KAAK,EAAoB,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC;oBACjE,CAAC,CAAC,SAAS;gBACb,SAAS,EAAE,iBAAiB;gBAC5B,IAAI;aACL,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,iBAAiB,CAAC,IAAI;gBACpB,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;YACD,mBAAmB,CAAC,IAAI;gBACtB,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACxC,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}