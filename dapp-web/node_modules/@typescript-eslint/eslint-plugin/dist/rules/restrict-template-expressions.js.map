{"version": 3, "file": "restrict-template-expressions.js", "sourceRoot": "", "sources": ["../../src/rules/restrict-template-expressions.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,2CAAuC;AAEvC,kCAQiB;AAQjB,MAAM,YAAY,GAChB,CAAC,YAAuB,EAAgB,EAAE,CAC1C,IAAI,CAAC,EAAE,CACL,IAAA,oBAAa,EAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AAEtC,MAAM,aAAa,GACjB;IACE,CAAC,KAAK,EAAE,oBAAa,CAAC;IACtB;QACE,OAAO;QACP,CAAC,IAAI,EAAE,OAAO,EAAE,oBAAoB,EAAW,EAAE,CAC/C,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACxD,oEAAoE;YACpE,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,EAAG,CAAC;KACnD;IACD,6EAA6E;IAC7E,CAAC,SAAS,EAAE,YAAY,CAAC,sBAAS,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC,SAAS,EAAE,YAAY,CAAC,sBAAS,CAAC,IAAI,GAAG,sBAAS,CAAC,SAAS,CAAC,CAAC;IAC/D,CAAC,QAAQ,EAAE,YAAY,CAAC,sBAAS,CAAC,UAAU,GAAG,sBAAS,CAAC,UAAU,CAAC,CAAC;IACrE;QACE,QAAQ;QACR,CAAC,IAAI,EAAE,OAAO,EAAW,EAAE,CAAC,IAAA,kBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,KAAK,QAAQ;KACpE;IACD,CAAC,OAAO,EAAE,sBAAe,CAAC;CAE7B,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;IACzB,IAAI;IACJ,MAAM,EAAE,QAAQ,IAAI,EAAW;IAC/B,MAAM;CACP,CAAC,CAAC,CAAC;AAOJ,kBAAe,IAAA,iBAAU,EAAqB;IAC5C,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,6DAA6D;YAC/D,WAAW,EAAE;gBACX,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE;oBACN;wBACE,QAAQ,EAAE,KAAK;wBACf,YAAY,EAAE,KAAK;wBACnB,YAAY,EAAE,KAAK;wBACnB,WAAW,EAAE,KAAK;wBAClB,WAAW,EAAE,KAAK;wBAClB,UAAU,EAAE,KAAK;qBAClB;iBACF;aACF;YACD,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,yDAAyD;SACvE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE,MAAM,CAAC,WAAW,CAC5B,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;oBACtC,MAAM;oBACN;wBACE,WAAW,EAAE,sBAAsB,IAAI,CAAC,WAAW,EAAE,0CAA0C;wBAC/F,IAAI,EAAE,SAAS;qBAChB;iBACF,CAAC,CACH;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI;SAClB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,oBAAoB,GAAG,aAAa,CAAC,MAAM,CAC/C,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAChC,CAAC;QAEF,OAAO;YACL,eAAe,CAAC,IAA8B;gBAC5C,uCAAuC;gBACvC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAAE,CAAC;oBACjE,OAAO;gBACT,CAAC;gBAED,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC1C,MAAM,cAAc,GAAG,IAAA,mCAA4B,EACjD,QAAQ,EACR,UAAU,CACX,CAAC;oBAEF,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,EAAE,CAAC;wBAC1C,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,SAAS,EAAE,aAAa;4BACxB,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE;yBACrD,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;QAEF,SAAS,oBAAoB,CAAC,SAAe;YAC3C,IAAI,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;gBACxB,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACrD,CAAC;YAED,IAAI,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC/B,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACpD,CAAC;YAED,OAAO,CACL,IAAA,oBAAa,EAAC,SAAS,EAAE,sBAAS,CAAC,UAAU,CAAC;gBAC9C,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CACvC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,oBAAoB,CAAC,CACjD,CACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}