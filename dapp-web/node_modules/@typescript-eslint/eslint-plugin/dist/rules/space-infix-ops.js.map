{"version": 3, "file": "space-infix-ops.js", "sourceRoot": "", "sources": ["../../src/rules/space-infix-ops.ts"], "names": [], "mappings": ";;AAAA,6DAA6D;AAC7D,oDAAqE;AAMrE,kCAA6D;AAC7D,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,iBAAiB,CAAC,CAAC;AAKtD,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAE1B,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,+BAA+B,CAAC;QAC7C,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,wCAAwC;YACrD,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;QAC9B,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE;YACR,mHAAmH;YACnH,YAAY,EAAE,yCAAyC;YACvD,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ;SAC1B;KACF;IACD,cAAc,EAAE;QACd;YACE,SAAS,EAAE,KAAK;SACjB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,SAAS,MAAM,CAAC,QAAwB;YACtC,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,cAAc;gBACzB,IAAI,EAAE;oBACJ,QAAQ,EAAE,QAAQ,CAAC,KAAK;iBACzB;gBACD,GAAG,CAAC,KAAK;oBACP,MAAM,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAClE,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBAC9D,IAAI,SAAS,GAAG,EAAE,CAAC;oBAEnB,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,aAAc,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;wBACtD,SAAS,GAAG,GAAG,CAAC;oBAClB,CAAC;oBAED,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC;oBAE5B,IAAI,UAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;wBACnD,SAAS,IAAI,GAAG,CAAC;oBACnB,CAAC;oBAED,OAAO,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBAChD,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,SAAS,WAAW,CAAC,KAAqB;YACxC,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CACzE,CAAC;QACJ,CAAC;QAED,SAAS,6BAA6B,CACpC,QAA+C,EAC/C,SAAiD;YAEjD,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,OAAO;YACT,CAAC;YAED,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,oBAAoB,CACtD,QAAQ,EACR,SAAS,EACT,WAAW,CACX,CAAC;YAEH,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAE,CAAC;YAC1D,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAE,CAAC;YAEzD,IACE,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC;gBAClD,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,EAClD,CAAC;gBACD,MAAM,CAAC,QAAQ,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QAED;;;WAGG;QACH,SAAS,2BAA2B,CAAC,IAA2B;YAC9D,6BAA6B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3D,CAAC;QAED;;;WAGG;QACH,SAAS,yCAAyC,CAChD,IAAiC;YAEjC,MAAM,QAAQ,GACZ,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc;gBACnC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,GAAG,CAAC;YAEtC,6BAA6B,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC;QAED;;;WAGG;QACH,SAAS,2BAA2B,CAClC,cAAkE;YAElE,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;YAEnC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnB,MAAM,uBAAuB,GAC3B,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,cAAc;oBAClD,CAAC,CAAC,6BAAsB;oBACxB,CAAC,CAAC,CAAC,CAAC;gBACR,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,cAAc,CAChD,IAAI,EACJ,uBAAuB,CACxB,CAAC;gBAEF,IAAI,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBACxD,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBACzD,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBAExD,IACE,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,IAAK,EAAE,QAAQ,CAAC;wBACnD,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAK,CAAC,EACnD,CAAC;wBACD,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED;;;WAGG;QACH,SAAS,2BAA2B,CAClC,IAAqC;YAErC,6BAA6B,CAC3B,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,EAAE,EAC9B,IAAI,CAAC,cAAc,CACpB,CAAC;QACJ,CAAC;QAED,SAAS,uBAAuB,CAAC,IAAgC;YAC/D,6BAA6B,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/D,6BAA6B,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO;YACL,GAAG,KAAK;YACR,YAAY,EAAE,2BAA2B;YACzC,kBAAkB,EAAE,yCAAyC;YAC7D,sBAAsB,EAAE,2BAA2B;YACnD,WAAW,EAAE,2BAA2B;YACxC,kBAAkB,EAAE,2BAA2B;YAC/C,iBAAiB,EAAE,uBAAuB;SAC3C,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}