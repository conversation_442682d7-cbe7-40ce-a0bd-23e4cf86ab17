{"version": 3, "file": "parse-options.js", "sourceRoot": "", "sources": ["../../../src/rules/naming-convention-utils/parse-options.ts"], "names": [], "mappings": ";;AA6FS,oCAAY;AA7FrB,qCAA0C;AAC1C,mCAOiB;AACjB,qCAA0C;AAO1C,2CAA8C;AAE9C,SAAS,eAAe,CAAC,MAAgB;IACvC,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;QAC9B,MAAM,IAAI,iBAAS,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IACH,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;QAC1B,MAAM,IAAI,qBAAa,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,sDAAsD;IACtD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;QAClB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,CAAC;IAED,MAAM,gBAAgB,GAAG;QACvB,iBAAiB;QACjB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,yBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3E,MAAM,EAAE,MAAM,CAAC,MAAM;YACnB,CAAC,CAAC;gBACE,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC;gBAC3C,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK;aAC3B;YACH,CAAC,CAAC,IAAI;QACR,iBAAiB,EACf,MAAM,CAAC,iBAAiB,KAAK,SAAS;YACpC,CAAC,CAAC,yBAAiB,CAAC,MAAM,CAAC,iBAAiB,CAAC;YAC7C,CAAC,CAAC,IAAI;QACV,kBAAkB,EAChB,MAAM,CAAC,kBAAkB,KAAK,SAAS;YACrC,CAAC,CAAC,yBAAiB,CAAC,MAAM,CAAC,kBAAkB,CAAC;YAC9C,CAAC,CAAC,IAAI;QACV,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;QACxE,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;QACxE,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,iBAAS,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;QAC3D,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,qBAAa,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;QACvD,MAAM,EACJ,MAAM,CAAC,MAAM,KAAK,SAAS;YACzB,CAAC,CAAC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ;gBACjC,CAAC,CAAC;oBACE,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;oBACrC,KAAK,EAAE,IAAI;iBACZ;gBACH,CAAC,CAAC;oBACE,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC;oBAC3C,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK;iBAC3B;YACL,CAAC,CAAC,IAAI;QACV,gDAAgD;QAChD,cAAc,EAAE,MAAM;KACvB,CAAC;IAEF,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC;QAC9C,CAAC,CAAC,MAAM,CAAC,QAAQ;QACjB,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAEtB,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAChC,QAAQ,EAAE,IAAA,uBAAc,EAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,qBAAa,CAAC,QAAQ,CAAC;YACzB,CAAC,CAAC,iBAAS,CAAC,QAAQ,CAAC;QACvB,GAAG,gBAAgB;KACpB,CAAC,CAAC,CAAC;AACN,CAAC;AAED,SAAS,YAAY,CAAC,OAAgB;IACpC,MAAM,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAEnE,MAAM,MAAM,GAAG,IAAA,mBAAY,EAAC,iBAAS,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;QACvD,GAAG,CAAC,CAAC,CAAC,GAAG,IAAA,2BAAe,EAAC,CAAC,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;QACxD,OAAO,GAAG,CAAC;QACX,2EAA2E;IAC7E,CAAC,EAAE,EAAmB,CAAC,CAAC;IAExB,OAAO,MAAM,CAAC;AAChB,CAAC"}