import React, { useState, useEffect } from "react";
import { contractService } from "../services/contractService";
import { TransactionHistory } from "./TransactionHistory";

interface CounterDAppProps {
  isWalletConnected: boolean;
}

export const CounterDApp: React.FC<CounterDAppProps> = ({
  isWalletConnected,
}) => {
  const [counterValue, setCounterValue] = useState<number>(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [incrementAmount, setIncrementAmount] = useState<number>(1);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [contractStatus, setContractStatus] = useState<any>(null);

  useEffect(() => {
    if (isWalletConnected && !isInitialized) {
      initializeContract();
    }
  }, [isWalletConnected, isInitialized]);

  const initializeContract = async () => {
    setIsLoading(true);
    try {
      await contractService.initialize();
      setIsInitialized(true);
      await refreshCounter();
    } catch (error) {
      console.error("Failed to initialize contract:", error);
      alert("Failed to initialize contract");
    } finally {
      setIsLoading(false);
    }
  };

  const refreshCounter = async () => {
    try {
      const value = await contractService.getValue();
      setCounterValue(value);
      updateStatus();
    } catch (error) {
      console.error("Failed to get counter value:", error);
    }
  };

  const updateStatus = () => {
    const status = (contractService as any).getStatus();
    const history = (contractService as any).getTransactionHistory();
    setContractStatus(status);
    setTransactions(history);
  };

  const handleIncrement = async () => {
    setIsLoading(true);
    try {
      await contractService.increment(incrementAmount);
      await refreshCounter();
    } catch (error) {
      console.error("Failed to increment:", error);
      alert("Failed to increment counter");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDecrement = async () => {
    setIsLoading(true);
    try {
      await contractService.decrement(incrementAmount);
      await refreshCounter();
    } catch (error) {
      console.error("Failed to decrement:", error);
      alert("Failed to decrement counter");
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = async () => {
    setIsLoading(true);
    try {
      await contractService.reset();
      await refreshCounter();
    } catch (error) {
      console.error("Failed to reset:", error);
      alert("Failed to reset counter");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isWalletConnected) {
    return (
      <div className="counter-dapp disabled">
        <h3>Counter DApp</h3>
        <p>Please connect your wallet to use the counter.</p>
      </div>
    );
  }

  return (
    <div className="counter-dapp">
      <h3>Counter DApp</h3>

      {!isInitialized ? (
        <div>
          <p>Initializing contract...</p>
          {isLoading && <div className="loading">Loading...</div>}
        </div>
      ) : (
        <div>
          <div className="counter-display">
            <h2>Current Value: {counterValue}</h2>
          </div>

          <div className="controls">
            <div className="amount-input">
              <label htmlFor="amount">Amount:</label>
              <input
                id="amount"
                type="number"
                min="1"
                value={incrementAmount}
                onChange={(e) =>
                  setIncrementAmount(Math.max(1, parseInt(e.target.value) || 1))
                }
                disabled={isLoading}
              />
            </div>

            <div className="buttons">
              <button
                onClick={handleIncrement}
                disabled={isLoading}
                className="increment-btn">
                Increment (+{incrementAmount})
              </button>

              <button
                onClick={handleDecrement}
                disabled={isLoading}
                className="decrement-btn">
                Decrement (-{incrementAmount})
              </button>

              <button
                onClick={handleReset}
                disabled={isLoading}
                className="reset-btn">
                Reset
              </button>
            </div>

            <button
              onClick={refreshCounter}
              disabled={isLoading}
              className="refresh-btn">
              Refresh Value
            </button>
          </div>

          {isLoading && <div className="loading">Processing...</div>}

          {contractStatus && (
            <div className="contract-status">
              <h4>📊 Contract Status</h4>
              <p>Transactions: {contractStatus.transactionCount}</p>
              <p>
                Status:{" "}
                {contractStatus.initialized
                  ? "✅ Initialized"
                  : "❌ Not Initialized"}
              </p>
            </div>
          )}

          <TransactionHistory transactions={transactions} />
        </div>
      )}
    </div>
  );
};
