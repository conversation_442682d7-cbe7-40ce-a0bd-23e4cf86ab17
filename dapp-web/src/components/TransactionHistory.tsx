import React from 'react';

interface Transaction {
  type: string;
  amount?: number;
  timestamp: Date;
  oldValue: number;
  newValue: number;
}

interface TransactionHistoryProps {
  transactions: Transaction[];
}

export const TransactionHistory: React.FC<TransactionHistoryProps> = ({ transactions }) => {
  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString();
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'initialize': return '🚀';
      case 'increment': return '📈';
      case 'decrement': return '📉';
      case 'reset': return '🔄';
      default: return '📋';
    }
  };

  const getTransactionDescription = (transaction: Transaction) => {
    switch (transaction.type) {
      case 'initialize':
        return 'Contract initialized';
      case 'increment':
        return `Incremented by ${transaction.amount}`;
      case 'decrement':
        return `Decremented by ${transaction.amount}`;
      case 'reset':
        return 'Counter reset';
      default:
        return transaction.type;
    }
  };

  if (transactions.length === 0) {
    return (
      <div className="transaction-history">
        <h3>📋 Transaction History</h3>
        <p className="no-transactions">No transactions yet. Start by connecting your wallet and initializing the contract!</p>
      </div>
    );
  }

  return (
    <div className="transaction-history">
      <h3>📋 Transaction History</h3>
      <div className="transaction-list">
        {transactions.slice(-5).reverse().map((transaction, index) => (
          <div key={index} className="transaction-item">
            <div className="transaction-icon">
              {getTransactionIcon(transaction.type)}
            </div>
            <div className="transaction-details">
              <div className="transaction-description">
                {getTransactionDescription(transaction)}
              </div>
              <div className="transaction-values">
                {transaction.oldValue} → {transaction.newValue}
              </div>
              <div className="transaction-time">
                {formatTime(transaction.timestamp)}
              </div>
            </div>
          </div>
        ))}
      </div>
      {transactions.length > 5 && (
        <div className="transaction-count">
          Showing last 5 of {transactions.length} transactions
        </div>
      )}
    </div>
  );
};
