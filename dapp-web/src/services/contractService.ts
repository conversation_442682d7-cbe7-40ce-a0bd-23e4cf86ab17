import { ContractAPI } from "../types/contract";

// Enhanced contract service that simulates real blockchain interactions
export class CounterContractService implements ContractAPI {
  private counterValue: number = 0;
  private isInitialized: boolean = false;
  private transactionHistory: Array<{
    type: string;
    amount?: number;
    timestamp: Date;
    oldValue: number;
    newValue: number;
  }> = [];

  async initialize(): Promise<void> {
    console.log("🚀 Initializing counter contract...");
    await this.simulateBlockchainDelay();

    this.counterValue = 0;
    this.isInitialized = true;
    this.addToHistory("initialize", undefined, 0, 0);

    console.log("✅ Counter contract initialized successfully");
  }

  async increment(amount: number): Promise<void> {
    if (!this.isInitialized) {
      throw new Error("Contract not initialized. Please initialize first.");
    }

    console.log(`📈 Incrementing counter by ${amount}...`);
    await this.simulateBlockchainDelay();

    const oldValue = this.counterValue;
    this.counterValue += amount;
    this.addToHistory("increment", amount, oldValue, this.counterValue);

    console.log(
      `✅ Counter incremented from ${oldValue} to ${this.counterValue}`
    );
  }

  async decrement(amount: number): Promise<void> {
    if (!this.isInitialized) {
      throw new Error("Contract not initialized. Please initialize first.");
    }

    console.log(`📉 Decrementing counter by ${amount}...`);
    await this.simulateBlockchainDelay();

    const oldValue = this.counterValue;
    this.counterValue = Math.max(0, this.counterValue - amount);
    this.addToHistory("decrement", amount, oldValue, this.counterValue);

    if (oldValue < amount) {
      console.log(
        `⚠️ Underflow protection: Counter set to 0 (was ${oldValue})`
      );
    } else {
      console.log(
        `✅ Counter decremented from ${oldValue} to ${this.counterValue}`
      );
    }
  }

  async reset(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error("Contract not initialized. Please initialize first.");
    }

    console.log("🔄 Resetting counter...");
    await this.simulateBlockchainDelay();

    const oldValue = this.counterValue;
    this.counterValue = 0;
    this.addToHistory("reset", undefined, oldValue, 0);

    console.log(`✅ Counter reset from ${oldValue} to 0`);
  }

  async getValue(): Promise<number> {
    if (!this.isInitialized) {
      throw new Error("Contract not initialized. Please initialize first.");
    }

    console.log(`📊 Current counter value: ${this.counterValue}`);
    return this.counterValue;
  }

  // Additional methods for enhanced functionality
  getTransactionHistory(): Array<{
    type: string;
    amount?: number;
    timestamp: Date;
    oldValue: number;
    newValue: number;
  }> {
    return [...this.transactionHistory];
  }

  getStatus(): {
    initialized: boolean;
    currentValue: number;
    transactionCount: number;
  } {
    return {
      initialized: this.isInitialized,
      currentValue: this.counterValue,
      transactionCount: this.transactionHistory.length,
    };
  }

  private addToHistory(
    type: string,
    amount: number | undefined,
    oldValue: number,
    newValue: number
  ): void {
    this.transactionHistory.push({
      type,
      amount,
      timestamp: new Date(),
      oldValue,
      newValue,
    });
  }

  private async simulateBlockchainDelay(): Promise<void> {
    const delay = Math.random() * 1500 + 800; // 800-2300ms delay to simulate real blockchain
    await new Promise((resolve) => setTimeout(resolve, delay));
  }
}

export const contractService = new CounterContractService();
