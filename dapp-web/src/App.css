.App {
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: white;
}

.App-header {
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.App-header h1 {
  margin: 0;
  font-size: 2.5rem;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.App-header p {
  margin: 0.5rem 0 0 0;
  opacity: 0.8;
  font-size: 1.1rem;
}

.App-main {
  flex: 1;
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.container {
  max-width: 1200px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  align-items: start;
}

@media (max-width: 1024px) {
  .container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: 800px;
  }
}

.wallet-connection,
.counter-dapp {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.wallet-connection h3,
.counter-dapp h3 {
  margin-top: 0;
  color: #ffd700;
  font-size: 1.5rem;
}

.connect-btn,
.disconnect-btn,
.increment-btn,
.decrement-btn,
.reset-btn,
.refresh-btn {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 0.25rem;
}

.connect-btn:hover,
.disconnect-btn:hover,
.increment-btn:hover,
.decrement-btn:hover,
.reset-btn:hover,
.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

.connect-btn:disabled,
.increment-btn:disabled,
.decrement-btn:disabled,
.reset-btn:disabled,
.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.disconnect-btn {
  background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%);
}

.disconnect-btn:hover {
  box-shadow: 0 4px 16px rgba(255, 107, 107, 0.4);
}

.counter-display {
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 2px solid #ffd700;
}

.counter-display h2 {
  margin: 0;
  font-size: 2rem;
  color: #ffd700;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.amount-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.amount-input label {
  font-weight: 600;
}

.amount-input input {
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  width: 80px;
  text-align: center;
}

.amount-input input:focus {
  outline: none;
  border-color: #ffd700;
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

.buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

.loading {
  margin: 1rem 0;
  padding: 0.5rem;
  background: rgba(255, 215, 0, 0.2);
  border-radius: 6px;
  color: #ffd700;
  font-weight: 600;
}

.counter-dapp.disabled {
  opacity: 0.6;
}

.counter-dapp.disabled p {
  color: #ccc;
}

.App-footer {
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.App-footer p {
  margin: 0.25rem 0;
  opacity: 0.7;
}

.App-footer a {
  color: #ffd700;
  text-decoration: none;
}

.App-footer a:hover {
  text-decoration: underline;
}

/* Transaction History Styles */
.transaction-history {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.transaction-history h3 {
  margin-top: 0;
  color: #ffd700;
  font-size: 1.2rem;
}

.no-transactions {
  color: #ccc;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

.transaction-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border-left: 3px solid #ffd700;
}

.transaction-icon {
  font-size: 1.2rem;
  min-width: 24px;
}

.transaction-details {
  flex: 1;
}

.transaction-description {
  font-weight: 600;
  color: white;
  margin-bottom: 0.25rem;
}

.transaction-values {
  font-size: 0.9rem;
  color: #ffd700;
  font-family: monospace;
}

.transaction-time {
  font-size: 0.8rem;
  color: #aaa;
  margin-top: 0.25rem;
}

.transaction-count {
  text-align: center;
  margin-top: 1rem;
  padding: 0.5rem;
  color: #aaa;
  font-size: 0.9rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Contract Status Styles */
.contract-status {
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.contract-status h4 {
  margin: 0 0 0.5rem 0;
  color: #ffd700;
  font-size: 1rem;
}

.contract-status p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}